import React from 'react';
import {
  View,
  Dimensions,
  PanResponder,
  Platform,
  BackHandler,
  ImageBackground,
} from 'react-native';
import { Surface, Shape, Path } from '@react-native-community/art';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import { IMIGotoPage } from '../../../../imilab-rn-sdk';
import { imiThemeManager, MessageDialog, showToast, showLoading } from '../../../../imilab-design-ui';
import { stringsTo } from '../../../../globalization/Localize';
import { LetDevice } from '../../../../imilab-rn-sdk/native/iot-kit/IMIDevice';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const viewWidth = screenWidth - 42; // 减去左右边距
const viewHeight = (viewWidth * 9) / 16; // 16:9比例
const REACT_MARGIN = 3; // 边距
const CIRCLE_RADIUS = 8; // 圆点半径
const itemWidth = 16; // 网格宽度
const itemHeight = 16; // 网格高度
const DEVIATION_VALUE = 10; // 偏差值

/**
 * 监控区域修改页面V2
 * 支持多种监控区域的绘制和编辑
 */
export default class MonitorAreaModifyPageV2 extends BaseDeviceComponent {
  constructor(props) {
    super(props);
    
    this.state = {
      progressing: false,
      showBg: false,
      showCover: true,
      canSave: false,
      showSaveDialog: false,
      // 监控区域状态控制
      allInPrivate: 0, // 0: 显示多个区域, 1: 红色警告状态, 2: 正常状态
      paths: [], // 存储多个监控区域的路径信息
    };

    // 触摸相关
    this.isMoving = false;
    this.timeStamp = Date.now();
    this.touchPosition = 0;

    // 设置默认的监控区域坐标
    const safeMargin = Math.max(REACT_MARGIN + 20, 30);
    this.rectDatas = [
      safeMargin,
      20,
      Math.min(viewWidth - safeMargin, 250),
      Math.min(viewHeight - 20, 150)
    ];

    // 背景网格区域
    this.rectBackGround = [
      Math.floor(this.rectDatas[0] / itemWidth) * itemWidth,
      Math.floor(this.rectDatas[1] / itemHeight) * itemHeight,
      Math.ceil(this.rectDatas[2] / itemWidth) * itemWidth,
      Math.ceil(this.rectDatas[3] / itemHeight) * itemHeight,
    ];

    // 初始化监控区域数据
    this._initializeMonitorAreas();
    
    // 创建手势响应器
    this._createPanResponders();
  }

  /**
   * 初始化监控区域数据
   */
  _initializeMonitorAreas() {
    // 模拟多个监控区域的数据
    const defaultPaths = [
      {
        path: this._createRectanglePath([50, 40, 150, 100]),
        color: '#32BAC0', // 青色 - 正常监控区域
        type: 'normal',
        id: 'area_1'
      },
      {
        path: this._createRectanglePath([200, 60, 280, 120]),
        color: '#FFD700', // 金色 - 重点监控区域
        type: 'important',
        id: 'area_2'
      },
      {
        path: this._createRectanglePath([100, 140, 200, 180]),
        color: '#FF6B6B', // 红色 - 警告区域
        type: 'warning',
        id: 'area_3'
      }
    ];

    this.setState({ paths: defaultPaths });
  }

  /**
   * 切换监控区域显示模式
   */
  _toggleAreaMode = (mode) => {
    this.setState({ allInPrivate: mode });
  }

  /**
   * 添加新的监控区域
   */
  _addNewArea = (coordinates, type = 'normal') => {
    const { paths } = this.state;
    const newArea = {
      path: this._createRectanglePath(coordinates),
      color: this._getColorByType(type),
      type: type,
      id: `area_${Date.now()}`
    };

    this.setState({ paths: [...paths, newArea] });
  }

  /**
   * 根据类型获取颜色
   */
  _getColorByType = (type) => {
    const colorMap = {
      'normal': '#32BAC0',
      'important': '#FFD700',
      'warning': '#FF6B6B',
      'privacy': '#9B59B6',
      'fence': '#E67E22'
    };
    return colorMap[type] || '#32BAC0';
  }

  /**
   * 删除监控区域
   */
  _removeArea = (areaId) => {
    const { paths } = this.state;
    const filteredPaths = paths.filter(area => area.id !== areaId);
    this.setState({ paths: filteredPaths });
  }

  /**
   * 创建矩形路径
   */
  _createRectanglePath(coords) {
    return Path()
      .moveTo(coords[0], coords[1])
      .lineTo(coords[2], coords[1])
      .lineTo(coords[2], coords[3])
      .lineTo(coords[0], coords[3])
      .close();
  }

  /**
   * 创建手势响应器
   */
  _createPanResponders() {
    // 主要的拖拽响应器
    this.myPanResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: (evt) => {
        this.isMoving = true;
        this.timeStamp = Date.now();
        this._handleTouchStart(evt);
      },
      onPanResponderMove: (evt) => {
        this._handleTouchMove(evt);
      },
      onPanResponderRelease: () => {
        this.isMoving = false;
        this._updateCanSaveState();
      },
    });

    // 四个角点的响应器
    this._createCornerResponders();
  }

  /**
   * 创建四个角点的响应器
   */
  _createCornerResponders() {
    const createCornerResponder = (position) => {
      return PanResponder.create({
        onStartShouldSetPanResponder: () => true,
        onMoveShouldSetPanResponder: () => true,
        onPanResponderGrant: () => {
          this.touchPosition = position;
          this.isMoving = true;
        },
        onPanResponderMove: (evt) => {
          this._handleCornerMove(evt, position);
        },
        onPanResponderRelease: () => {
          this.isMoving = false;
          this._updateCanSaveState();
        },
      });
    };

    this.leftTopPanResponder = createCornerResponder(12); // 左上角
    this.rightTopPanResponder = createCornerResponder(6); // 右上角
    this.leftBottomPanResponder = createCornerResponder(10); // 左下角
    this.rightBottomPanResponder = createCornerResponder(4); // 右下角
  }

  /**
   * 处理触摸开始
   */
  _handleTouchStart(evt) {
    const { locationX, locationY } = evt.nativeEvent;
    // 根据触摸位置确定操作类型
    console.log('触摸开始:', locationX, locationY);
  }

  /**
   * 处理触摸移动
   */
  _handleTouchMove(evt) {
    if (!this.isMoving) return;
    
    const { locationX, locationY } = evt.nativeEvent;
    // 更新区域坐标
    this._updateRectCoordinates(locationX, locationY);
  }

  /**
   * 处理角点移动
   */
  _handleCornerMove(evt, position) {
    const { locationX, locationY } = evt.nativeEvent;
    this._updateCornerPosition(locationX, locationY, position);
  }

  /**
   * 更新矩形坐标
   */
  _updateRectCoordinates(x, y) {
    // 实现坐标更新逻辑
    this.setState({}); // 触发重新渲染
  }

  /**
   * 更新角点位置
   */
  _updateCornerPosition(x, y, position) {
    let pointChange = false;
    
    switch (position) {
      case 12: // 左上角
        if (x < this.rectDatas[2] - itemWidth + DEVIATION_VALUE) {
          pointChange = true;
          this.rectDatas[0] = Math.max(0, x);
          this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
        }
        if (y < this.rectDatas[3] - itemHeight + DEVIATION_VALUE) {
          pointChange = true;
          this.rectDatas[1] = Math.max(0, y);
          this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
        }
        break;
      case 6: // 右上角
        if (x < viewWidth - REACT_MARGIN && x > this.rectDatas[0] + itemWidth - DEVIATION_VALUE) {
          pointChange = true;
          this.rectDatas[2] = x;
          this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
        }
        if (y >= REACT_MARGIN && y < this.rectDatas[3] - itemHeight + DEVIATION_VALUE) {
          pointChange = true;
          this.rectDatas[1] = y;
          this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
        }
        break;
      case 10: // 左下角
        if (x < this.rectDatas[2] - itemWidth + DEVIATION_VALUE) {
          pointChange = true;
          this.rectDatas[0] = Math.max(0, x);
          this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
        }
        if (y > this.rectDatas[1] + itemHeight - DEVIATION_VALUE && y < viewHeight - REACT_MARGIN) {
          pointChange = true;
          this.rectDatas[3] = y;
          this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
        }
        break;
      case 4: // 右下角
        if (x < viewWidth - REACT_MARGIN && x > this.rectDatas[0] + itemWidth - DEVIATION_VALUE) {
          pointChange = true;
          this.rectDatas[2] = x;
          this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
        }
        if (y > this.rectDatas[1] + itemHeight - DEVIATION_VALUE && y < viewHeight - REACT_MARGIN) {
          pointChange = true;
          this.rectDatas[3] = y;
          this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
        }
        break;
    }

    if (pointChange) {
      this.setState({}); // 触发重新渲染
    }
  }

  /**
   * 更新保存状态
   */
  _updateCanSaveState() {
    const canSave = this._validateAreaSize();
    this.setState({ canSave });
  }

  /**
   * 验证区域大小
   */
  _validateAreaSize() {
    const width = Math.abs(this.rectDatas[2] - this.rectDatas[0]);
    const height = Math.abs(this.rectDatas[3] - this.rectDatas[1]);
    return width >= itemWidth * 2 && height >= itemHeight * 2;
  }

  componentDidMount() {
    if (Platform.OS === 'android') {
      BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
    }

    // 获取传入的参数
    const { allInPrivate = 0, areaData, paths } = this.props.route?.params || {};
    
    if (areaData) {
      this.rectDatas = areaData;
    }
    
    if (paths) {
      this.setState({ paths });
    }

    this.setState({ allInPrivate });

    setTimeout(() => {
      this.setState({ showBg: true, showCover: false });
    }, 100);
  }

  componentWillUnmount() {
    if (Platform.OS === 'android') {
      BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
    }
  }

  onBackHandler = () => {
    this.props.navigation.goBack();
    return true;
  };

  /**
   * 保存监控区域设置
   */
  _saveAreaSettings = () => {
    if (!this.state.canSave) {
      showToast('请调整监控区域大小');
      return;
    }

    this.setState({ progressing: true });
    showLoading('保存中...', true);

    // 构建区域数据
    const areaData = {
      coordinates: this.rectDatas,
      type: this.state.allInPrivate,
      paths: this.state.paths
    };

    // 模拟保存操作
    setTimeout(() => {
      const callback = this.props.route?.params?.callback;
      if (callback) {
        callback(areaData);
      }
      
      showToast('保存成功');
      this.setState({ progressing: false });
      showLoading(false);
      this.props.navigation.goBack();
    }, 1000);
  };

  render() {
    const { allInPrivate, paths } = this.state;
    
    // 可拖拽线框的绘制路径
    let draggable_rectangle_path = Path()
      .moveTo(this.rectDatas[0], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[3])
      .lineTo(this.rectDatas[0], this.rectDatas[3])
      .close();

    // 四个背景遮罩区域的绘制路径
    let background_path_one = Path()
      .moveTo(0, 0)
      .lineTo(this.rectDatas[0], 0)
      .lineTo(this.rectDatas[0], viewHeight)
      .lineTo(0, viewHeight)
      .close();

    let background_path_two = Path()
      .moveTo(this.rectDatas[0], 0)
      .lineTo(viewWidth, 0)
      .lineTo(viewWidth, this.rectDatas[1])
      .lineTo(this.rectDatas[0], this.rectDatas[1])
      .close();

    let background_path_three = Path()
      .moveTo(this.rectDatas[0], this.rectDatas[3])
      .lineTo(viewWidth, this.rectDatas[3])
      .lineTo(viewWidth, viewHeight)
      .lineTo(this.rectDatas[0], viewHeight)
      .close();

    let background_path_four = Path()
      .moveTo(this.rectDatas[2], this.rectDatas[1])
      .lineTo(viewWidth, this.rectDatas[1])
      .lineTo(viewWidth, this.rectDatas[3])
      .lineTo(this.rectDatas[2], this.rectDatas[3])
      .close();

    // 四个角点圆圈的绘制路径
    let top_left_circle = new Path()
      .moveTo(this.rectDatas[0] + CIRCLE_RADIUS, this.rectDatas[1])
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let top_right_circle = new Path()
      .moveTo(this.rectDatas[2] + CIRCLE_RADIUS, this.rectDatas[1])
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let bottom_right_circle = new Path()
      .moveTo(this.rectDatas[2] + CIRCLE_RADIUS, this.rectDatas[3] + 2 * CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let bottom_left_circle = new Path()
      .moveTo(this.rectDatas[0] + CIRCLE_RADIUS, this.rectDatas[3] + 2 * CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    return (
      <View style={{ flex: 1, backgroundColor: '#000000' }}>
        <NavigationBar
          title="监控区域设置"
          onBackPress={this.onBackHandler}
          rightText="保存"
          rightTextStyle={{ color: this.state.canSave ? '#32BAC0' : '#666666' }}
          onRightPress={this._saveAreaSettings}
        />
        
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ImageBackground
            style={{
              width: viewWidth,
              height: viewHeight,
              marginHorizontal: 21,
              marginTop: 10,
              marginBottom: 20,
            }}
            imageStyle={{ borderRadius: 0 }}
            {...this.myPanResponder.panHandlers}>
            
            <View style={{ position: 'absolute' }}>
              <Surface width={viewWidth} height={viewHeight}>
                {/* 背景遮罩 */}
                <Shape d={background_path_one} fill="#000000" opacity="0.5" />
                <Shape d={background_path_two} fill="#000000" opacity="0.5" />
                <Shape d={background_path_three} fill="#000000" opacity="0.5" />
                <Shape d={background_path_four} fill="#000000" opacity="0.5" />
                
                {/* 根据状态显示不同的边框 */}
                {allInPrivate === 1 ? (
                  <Shape d={draggable_rectangle_path} stroke="#ff0000" strokeWidth={1} />
                ) : allInPrivate === 2 ? (
                  <Shape d={draggable_rectangle_path} stroke="#32BAC0" strokeWidth={1} />
                ) : null}
                
                {/* 显示多个监控区域路径 */}
                {allInPrivate === 0 ? (
                  paths.map((item, index) => (
                    <Shape key={index} d={item.path} stroke={item.color} strokeWidth={1} />
                  ))
                ) : null}

                {/* 四个角点圆圈 */}
                <Shape d={top_left_circle} fill="#32BAC0" />
                <Shape d={top_right_circle} fill="#32BAC0" />
                <Shape d={bottom_right_circle} fill="#32BAC0" />
                <Shape d={bottom_left_circle} fill="#32BAC0" />
              </Surface>
            </View>
          </ImageBackground>
        </View>
      </View>
    );
  }
}
